'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { ContentStatus } from '@/lib/types';

interface ToolBulkActionsProps {
  selectedCount: number;
  selectedToolIds: string[];
  onBulkStatusUpdate: (toolIds: string[], status: ContentStatus) => Promise<void>;
  onBulkDelete: (toolIds: string[]) => Promise<void>;
  onClearSelection: () => void;
}

interface BulkActionConfig {
  action: 'updateStatus' | 'delete';
  label: string;
  icon: string;
  color: string;
  confirmationRequired: boolean;
  description: string;
}

interface StatusUpdateConfig {
  status: ContentStatus;
  label: string;
  icon: string;
  color: string;
  description: string;
}

/**
 * Confirmation Dialog Component
 */
function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  description,
  icon,
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  isDestructive = false,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  description: string;
  icon: string;
  confirmLabel?: string;
  cancelLabel?: string;
  isDestructive?: boolean;
}) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6 max-w-md mx-4">
        <div className="text-center">
          <div className="text-4xl mb-4">{icon}</div>
          <h3 className="text-lg font-semibold text-white mb-2">
            {title}
          </h3>
          <p className="text-gray-300 mb-4">
            {message}
          </p>
          <p className="text-sm text-gray-400 mb-6">
            {description}
          </p>
          <div className="flex space-x-3 justify-center">
            <Button
              variant="outline"
              onClick={onClose}
              className="px-4 py-2"
            >
              {cancelLabel}
            </Button>
            <Button
              variant="primary"
              onClick={onConfirm}
              className={`px-4 py-2 ${
                isDestructive 
                  ? 'bg-red-600 hover:bg-red-700' 
                  : 'bg-orange-500 hover:bg-orange-600'
              }`}
            >
              {confirmLabel}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Status Update Dialog Component
 */
function StatusUpdateDialog({
  isOpen,
  onClose,
  onConfirm,
  selectedCount,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (status: ContentStatus) => void;
  selectedCount: number;
}) {
  const [selectedStatus, setSelectedStatus] = useState<ContentStatus>('draft');

  const statusOptions: StatusUpdateConfig[] = [
    {
      status: 'draft',
      label: 'Draft',
      icon: '📝',
      color: 'bg-yellow-500/20 text-yellow-400',
      description: 'Move to draft status'
    },
    {
      status: 'under_review',
      label: 'Under Review',
      icon: '👀',
      color: 'bg-blue-500/20 text-blue-400',
      description: 'Submit for editorial review'
    },
    {
      status: 'approved',
      label: 'Approved',
      icon: '✅',
      color: 'bg-green-500/20 text-green-400',
      description: 'Mark as approved'
    },
    {
      status: 'published',
      label: 'Published',
      icon: '🌐',
      color: 'bg-green-600/20 text-green-300',
      description: 'Publish to live site'
    },
    {
      status: 'rejected',
      label: 'Rejected',
      icon: '❌',
      color: 'bg-red-500/20 text-red-400',
      description: 'Mark as rejected'
    },
    {
      status: 'archived',
      label: 'Archived',
      icon: '📦',
      color: 'bg-gray-500/20 text-gray-400',
      description: 'Archive tools'
    }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6 max-w-lg mx-4">
        <div className="text-center mb-6">
          <h3 className="text-lg font-semibold text-white mb-2">
            Update Status
          </h3>
          <p className="text-gray-300">
            Update status for {selectedCount} selected tool{selectedCount !== 1 ? 's' : ''}
          </p>
        </div>

        <div className="space-y-2 mb-6">
          {statusOptions.map((option) => (
            <label
              key={option.status}
              className={`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${
                selectedStatus === option.status
                  ? 'border-orange-500 bg-orange-500/10'
                  : 'border-zinc-600 hover:border-zinc-500 hover:bg-zinc-700/50'
              }`}
            >
              <input
                type="radio"
                name="status"
                value={option.status}
                checked={selectedStatus === option.status}
                onChange={(e) => setSelectedStatus(e.target.value as ContentStatus)}
                className="sr-only"
              />
              <div className="flex items-center space-x-3 flex-1">
                <span className="text-2xl">{option.icon}</span>
                <div className="flex-1">
                  <div className="text-white font-medium">{option.label}</div>
                  <div className="text-sm text-gray-400">{option.description}</div>
                </div>
              </div>
            </label>
          ))}
        </div>

        <div className="flex space-x-3 justify-center">
          <Button
            variant="outline"
            onClick={onClose}
            className="px-4 py-2"
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={() => onConfirm(selectedStatus)}
            className="px-4 py-2 bg-orange-500 hover:bg-orange-600"
          >
            Update Status
          </Button>
        </div>
      </div>
    </div>
  );
}

/**
 * Tool Bulk Actions Component
 * 
 * Provides bulk operations for selected tools including status updates and deletion.
 * Features:
 * - Bulk status updates with confirmation
 * - Bulk delete with strong confirmation
 * - Clear selection functionality
 * - Consistent styling with existing admin interface
 */
export function ToolBulkActions({
  selectedCount,
  selectedToolIds,
  onBulkStatusUpdate,
  onBulkDelete,
  onClearSelection,
}: ToolBulkActionsProps): React.JSX.Element {
  const [isProcessing, setIsProcessing] = useState(false);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  const handleStatusUpdate = async (status: ContentStatus) => {
    setIsProcessing(true);
    try {
      await onBulkStatusUpdate(selectedToolIds, status);
      setShowStatusDialog(false);
    } catch (error) {
      console.error('Bulk status update failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkDelete = async () => {
    setIsProcessing(true);
    try {
      await onBulkDelete(selectedToolIds);
      setShowDeleteConfirmation(false);
    } catch (error) {
      console.error('Bulk delete failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  if (selectedCount === 0) return <></>;

  return (
    <>
      <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-white">
                {selectedCount} tool{selectedCount !== 1 ? 's' : ''} selected
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={onClearSelection}
                className="text-xs"
              >
                Clear Selection
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="primary"
              size="sm"
              onClick={() => setShowStatusDialog(true)}
              disabled={isProcessing || selectedCount === 0}
              className="bg-blue-600 hover:bg-blue-700 text-xs"
            >
              📝 Update Status
            </Button>
            <Button
              variant="primary"
              size="sm"
              onClick={() => setShowDeleteConfirmation(true)}
              disabled={isProcessing || selectedCount === 0}
              className="bg-red-600 hover:bg-red-700 text-xs"
            >
              🗑️ Delete
            </Button>
          </div>
        </div>
      </div>

      {/* Status Update Dialog */}
      <StatusUpdateDialog
        isOpen={showStatusDialog}
        onClose={() => setShowStatusDialog(false)}
        onConfirm={handleStatusUpdate}
        selectedCount={selectedCount}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showDeleteConfirmation}
        onClose={() => setShowDeleteConfirmation(false)}
        onConfirm={handleBulkDelete}
        title="Confirm Deletion"
        message={`Are you sure you want to delete ${selectedCount} selected tool${selectedCount !== 1 ? 's' : ''}?`}
        description="This action cannot be undone. All tool data will be permanently removed."
        icon="🗑️"
        confirmLabel="Delete"
        cancelLabel="Cancel"
        isDestructive={true}
      />
    </>
  );
}
