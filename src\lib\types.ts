/**
 * Database Tool Interface - Represents the exact database schema
 * All field names match the actual database column names (snake_case)
 */
export interface DbTool {
  // Core fields (original schema)
  id: string;
  name: string;
  slug: string;
  logo_url?: string;
  description?: string;
  short_description?: string;
  detailed_description?: string;
  link: string; // URL slug like /tools/chatgpt (internal tool page)
  website?: string; // Main website URL (external)
  category_id?: string;
  subcategory?: string;
  company?: string;
  founded?: string;
  is_verified?: boolean;
  is_claimed?: boolean;
  features?: any; // JSONB
  screenshots?: any; // JSONB
  pricing?: any; // JSONB
  social_links?: any; // JSONB
  pros_and_cons?: any; // JSONB
  haiku?: any; // JSONB
  hashtags?: any; // JSONB
  releases?: any; // JSONB
  claim_info?: any; // JSONB
  meta_title?: string;
  meta_description?: string;
  content_status?: ContentStatus;
  generated_content?: any; // JSONB
  created_at?: string;
  updated_at?: string;
  published_at?: string;

  // Enhanced AI System fields (added via migration)
  scraped_data?: any; // JSONB
  ai_generation_status?: AIGenerationStatus;
  last_scraped_at?: string;
  editorial_review_id?: string;
  ai_generation_job_id?: string;
  submission_type?: SubmissionType;
  submission_source?: string;
  content_quality_score?: number;
  last_ai_update?: string;
}

/**
 * Frontend Tool Interface - Represents the transformed data for frontend use
 * Uses camelCase naming and typed structures for better developer experience
 */
export interface AITool {
  // Core fields
  id: string;
  name: string;
  slug?: string;
  logoUrl: string;
  description: string;
  shortDescription?: string;
  detailedDescription?: string;
  link: string; // URL slug like /tools/chatgpt (internal tool page)
  website?: string; // Main website URL (external)
  category: string;
  subcategory?: string;
  company?: string;
  founded?: string;

  // Verification and claiming
  isVerified?: boolean;
  isClaimed?: boolean;
  claimInfo?: {
    isClaimable: boolean;
    claimUrl?: string;
    claimInstructions?: string;
  };

  // Content fields
  features?: string[];
  screenshots?: string[];
  pricing?: {
    type: 'free' | 'freemium' | 'paid' | 'open source' | 'subscription';
    plans?: {
      name: string;
      price: string;
      features: string[];
    }[];
  };
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
  prosAndCons?: {
    pros: string[];
    cons: string[];
  };
  releases?: {
    version: string;
    date: string;
    notes: string;
    isLatest?: boolean;
  }[];
  haiku?: any;
  hashtags?: string[];
  tags?: {
    type: 'Trending' | 'New' | 'Premium' | 'AI' | 'HOT' | 'NEW' | 'PREMIUM';
    label?: string;
    icon?: string;
  }[];

  // SEO fields
  metaTitle?: string;
  metaDescription?: string;

  // Status and timestamps
  contentStatus?: ContentStatus;
  generatedContent?: any;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;

  // Enhanced AI System fields
  scrapedData?: ScrapedData;
  aiGenerationStatus?: AIGenerationStatus;
  lastScrapedAt?: string;
  editorialReviewId?: string;
  aiGenerationJobId?: string;
  submissionType?: SubmissionType;
  submissionSource?: string;
  contentQualityScore?: number;
  lastAiUpdate?: string;

  // Legacy/compatibility fields
  reviews?: {
    rating: number;
    totalReviews: number;
    highlights?: string[];
  };
}

/**
 * Database Category Interface - Represents the exact database schema
 * All field names match the actual database column names (snake_case)
 */
export interface DbCategory {
  id: string;
  title: string;
  icon_name?: string;
  description?: string;
  meta_title?: string;
  meta_description?: string;
  color_class?: string;
  text_color_class?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AICategory {
  id: string;
  title: string;
  iconName: string;
  description: string;
  tools: AITool[];
  totalToolsCount: number;
  seeAllButton: {
    colorClass: string;
    textColorClass: string;
  };
}

export interface TooltipData {
  content: string;
  targetRect: DOMRect | null;
  position: 'top' | 'bottom' | 'left' | 'right';
}

export interface TopSearch {
  term: string;
  link: string;
}

export interface ToolFilters {
  search?: string;
  tags?: string[];
  pricing?: 'free' | 'freemium' | 'paid' | 'open source' | 'subscription';
  verified?: boolean;
  sortBy?: 'name' | 'rating' | 'newest' | 'popular' | 'created_at';
  sortOrder?: 'asc' | 'desc';
  // Additional filters for API compatibility
  category?: string;
  subcategory?: string;
  page?: number;
  limit?: number;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface CategoryPageData {
  category: AICategory;
  subcategory?: string;
  tools: AITool[];
  filters: ToolFilters;
  pagination: PaginationInfo;
}

export interface HomePageState {
  searchTerm: string;
  isSearchDropdownVisible: boolean;
  topSearches: TopSearch[];
  allCategories: AICategory[];
  searchResults: AITool[] | null;
  isLoadingCategories: boolean;
  isLoadingSearchResults: boolean;
  activeTooltip: TooltipData | null;
  isScrollToTopVisible: boolean;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  isExpanded?: boolean;
}

// =====================================================
// ENHANCED AI SYSTEM TYPES
// =====================================================

// AI Generation Status
export type AIGenerationStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';

// Content Status
export type ContentStatus = 'draft' | 'published' | 'archived' | 'under_review' | 'approved' | 'rejected';

// Submission Types
export type SubmissionType = 'admin' | 'user_url' | 'user_full';

// Job Status
export type JobStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'paused';

// Job Types
export type JobType = 'scrape' | 'generate' | 'bulk' | 'media_extraction';

// Asset Types
export type AssetType = 'logo' | 'favicon' | 'screenshot' | 'og_image';

// Review Status
export type ReviewStatus = 'pending' | 'approved' | 'rejected' | 'needs_revision';

// Scraped Data Interface
export interface ScrapedData {
  url: string;
  title?: string;
  description?: string;
  content: string; // Markdown content
  metadata: {
    ogImage?: string;
    favicon?: string;
    keywords?: string[];
    author?: string;
    publishedDate?: string;
  };
  pricing?: {
    detected: boolean;
    plans?: any[];
    freeOption?: boolean;
  };
  features?: string[];
  screenshots?: string[];
  scrapedAt: string;
  scrapeMethod: string; // 'scrape.do', 'manual', etc.
}

// AI Generation Job Interface
export interface AIGenerationJob {
  id: string;
  toolId: string;
  jobType: JobType;
  status: JobStatus;
  progress: number; // 0-100
  scrapedData?: ScrapedData;
  aiPrompts?: {
    systemPrompt: string;
    userPrompts: string[];
    modelUsed: string;
  };
  aiResponses?: {
    generatedContent: any;
    tokensUsed: number;
    cost: number;
    processingTime: number;
  };
  errorLogs?: {
    timestamp: string;
    error: string;
    stackTrace?: string;
  }[];
  processingOptions?: {
    aiProvider: 'openai' | 'openrouter';
    model: string;
    temperature: number;
    maxTokens: number;
  };
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
}

// Media Asset Interface
export interface MediaAsset {
  id: string;
  toolId: string;
  assetType: AssetType;
  sourceUrl?: string;
  localPath?: string;
  cdnUrl?: string;
  fileSize?: number;
  mimeType?: string;
  width?: number;
  height?: number;
  altText?: string;
  isPrimary: boolean;
  extractionMethod: string;
  createdAt: string;
  updatedAt: string;
}

// Editorial Review Interface
export interface EditorialReview {
  id: string;
  toolId: string;
  reviewedBy: string;
  reviewStatus: ReviewStatus;
  reviewDate: string;
  featuredDate?: string;
  reviewNotes?: string;
  editorialText?: string; // "was manually vetted by our editorial team and was first featured on [date]"
  qualityScore?: number; // 1-10
  contentFlags?: string[];
  approvalWorkflow?: {
    currentStep: string;
    history: {
      step: string;
      timestamp: string;
      user: string;
      notes?: string;
    }[];
  };
  createdAt: string;
  updatedAt: string;
}

// Legacy Bulk Processing Job Interface (deprecated - use InternalBulkProcessingJob)
// This interface is kept for backward compatibility with existing code
export interface LegacyBulkProcessingJob {
  id: string;
  jobType: 'text_file' | 'json_file' | 'manual_entry' | 'csv_import';
  status: JobStatus;
  progress?: number; // 0-100 percentage
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  data?: any; // Additional job data for progress tracking
  sourceData: {
    filename?: string;
    urls?: string[];
    tools?: Partial<AITool>[];
  };
  processingOptions: {
    batchSize: number;
    delayBetweenBatches: number;
    retryAttempts: number;
    aiProvider: 'openai' | 'openrouter';
    skipExisting: boolean;
  };
  results: {
    successful: {
      toolId: string;
      url: string;
      generatedAt: string;
    }[];
    failed: {
      url: string;
      error: string;
      timestamp: string;
    }[];
  };
  progressLog: {
    timestamp: string;
    message: string;
    level: 'info' | 'warning' | 'error';
  }[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
}

// System Configuration Interface
export interface SystemConfiguration {
  id: string;
  configKey: string;
  configValue: any;
  configType: 'ai_provider' | 'scraping' | 'job_processing' | 'system' | 'security';
  isSensitive: boolean;
  isActive: boolean;
  description?: string;
  validationSchema?: any;
  updatedBy?: string;
  version: number;
  createdAt: string;
  updatedAt: string;
}

// =====================================================
// ADMIN PANEL INTERFACES
// =====================================================

// Admin Dashboard Stats
export interface AdminDashboardStats {
  totalTools: number;
  publishedTools: number;
  draftTools: number;
  pendingReview: number;
  activeJobs: number;
  completedJobs: number;
  failedJobs: number;
  totalMediaAssets: number;
  systemHealth: {
    status: 'healthy' | 'warning' | 'error';
    uptime: number;
    lastCheck: string;
  };
}

// Job Monitoring Interface
export interface JobMonitoringData {
  activeJobs: AIGenerationJob[];
  recentJobs: AIGenerationJob[];
  jobStats: {
    totalJobs: number;
    successRate: number;
    averageProcessingTime: number;
    costToday: number;
    costThisMonth: number;
  };
  systemMetrics: {
    queueLength: number;
    processingCapacity: number;
    errorRate: number;
  };
}

// Bulk Tool Data Interface
export interface BulkToolData {
  url: string;
  providedData: {
    name?: string | null;
    category?: string | null;
    description?: string | null;
    pricing?: any | null;
    features?: string[] | null;
  };
  needsGeneration: {
    name: boolean;
    description: boolean;
    features: boolean;
    pricing: boolean;
    prosAndCons: boolean;
    haiku: boolean;
    hashtags: boolean;
  };
}

// Bulk Processing Interface
export interface BulkProcessingData {
  activeJobs: InternalBulkProcessingJob[];
  recentJobs: InternalBulkProcessingJob[];
  templates: {
    textFile: string;
    jsonFile: any;
    csvFile: string;
  };
  processingOptions: {
    defaultBatchSize: number;
    maxConcurrentJobs: number;
    supportedFormats: string[];
  };
}

// Editorial Workflow Interface
export interface EditorialWorkflowData {
  pendingReviews: (AITool & { editorialReview?: InternalEditorialReview })[];
  recentReviews: InternalEditorialReview[];
  reviewStats: {
    totalReviews: number;
    approvalRate: number;
    averageReviewTime: number;
    featuredTools: number;
  };
  workflowSettings: {
    autoApprovalThreshold: number;
    requiredReviewers: number;
    escalationRules: any[];
  };
}

// =====================================================
// CONTENT GENERATION PIPELINE TYPES
// =====================================================

// Pipeline Status
export type PipelineStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'pending_review' | 'pending_manual_review';

// Workflow State
export type WorkflowState = 'draft' | 'ai_generation' | 'validation' | 'quality_scoring' | 'editorial_review' | 'manual_review' | 'approved' | 'rejected' | 'published' | 'error';

// Review Decision
export type ReviewDecision = 'approve' | 'reject' | 'request_changes';

// Content Generation Request
export interface ContentGenerationRequest {
  toolId: string;
  scrapedContent: string;
  toolUrl: string;
  complexity?: 'simple' | 'medium' | 'complex';
  priority?: 'speed' | 'quality' | 'cost';
  contentQuality?: number; // 0-100
  scrapingCost?: number;
}

// Content Generation Result
export interface ContentGenerationResult {
  success: boolean;
  toolId: string;
  status: PipelineStatus;
  workflowState: WorkflowState;
  generatedContent?: any;
  validation?: ValidationResult;
  qualityScore?: QualityScore;
  editorialReview?: InternalEditorialReview;
  error?: string;
  metadata: {
    startTime: string;
    endTime?: string;
    duration?: number;
    modelUsed?: string;
    tokenUsage?: any;
    steps: string[];
  };
}

// Validation Error
export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
  value: any;
}

// Validation Result
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: string[];
  score: number; // 0-100
  contentLength: number;
  url: string;
  validatedAt: string;
}

// Quality Metrics
export interface QualityMetrics {
  completeness: number;
  accuracy: number;
  engagement: number;
  structure: number;
  relevance: number;
  consistency: number;
}

// Quality Flag
export interface QualityFlag {
  type: 'completeness' | 'accuracy' | 'validation' | 'error';
  severity: 'low' | 'medium' | 'high';
  message: string;
  field: string;
}

// Quality Score
export interface QualityScore {
  overall: number;
  breakdown: QualityMetrics;
  level: 'excellent' | 'good' | 'acceptable' | 'poor';
  flags: QualityFlag[];
  suggestions: string[];
  scoredAt: string;
  version: string;
}

// Content Standards
export interface ContentStandards {
  minDescriptionLength: number;
  maxDescriptionLength: number;
  minFeaturesCount: number;
  maxFeaturesCount: number;
  minProsConsCount: number;
  maxProsConsCount: number;
  requiredFields: string[];
  bannedWords: string[];
  validPricingTypes: string[];
}

// Approval Workflow
export interface ApprovalWorkflow {
  currentStep: string;
  history: {
    step: string;
    timestamp: string;
    user: string;
    notes?: string;
  }[];
}

// Manual Review Data
export interface ManualReviewData {
  review: InternalEditorialReview;
  tool: any;
  priority: 'high' | 'normal' | 'low';
  estimatedReviewTime: number;
  flags: string[];
}

// Editorial Text Validation
export interface EditorialTextValidation {
  isValid: boolean;
  errors: string[];
  template: string;
  example: string;
}

// User Submission Management
export interface UserSubmissionData {
  pendingSubmissions: {
    id: string;
    url: string;
    name: string;
    description: string;
    submitterEmail: string;
    submittedAt: string;
    status: 'pending' | 'approved' | 'rejected';
  }[];
  submissionStats: {
    totalSubmissions: number;
    approvalRate: number;
    averageProcessingTime: number;
    topCategories: { category: string; count: number }[];
  };
}

// Configuration Management Interface
export interface ConfigurationManagementData {
  aiProviders: {
    openai: {
      enabled: boolean;
      apiKey: string;
      model: string;
      maxTokens: number;
      temperature: number;
    };
    openrouter: {
      enabled: boolean;
      apiKey: string;
      model: string;
      maxTokens: number;
      temperature: number;
    };
  };
  scraping: {
    scrapeDoApiKey: string;
    defaultTimeout: number;
    costOptimizationEnabled: boolean;
    maxRetries: number;
  };
  jobProcessing: {
    maxConcurrentJobs: number;
    defaultRetryAttempts: number;
    batchSize: number;
    processingDelay: number;
  };
  system: {
    contentQualityThreshold: number;
    autoApprovalEnabled: boolean;
    debugMode: boolean;
    maintenanceMode: boolean;
  };
}

// API Response Types
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

// Pagination Interface
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Search and Filter Interfaces
export interface AdminToolFilters extends ToolFilters {
  aiGenerationStatus?: AIGenerationStatus;
  contentStatus?: ContentStatus;
  submissionType?: SubmissionType;
  hasEditorialReview?: boolean;
  qualityScoreMin?: number;
  qualityScoreMax?: number;
  lastScrapedAfter?: string;
  lastScrapedBefore?: string;
}

// WebSocket Event Types for Real-time Updates
export interface WebSocketEvent {
  type: 'job_update' | 'job_completed' | 'job_failed' | 'system_alert' | 'bulk_progress';
  data: any;
  timestamp: string;
}

// =====================================================
// MIGRATION SYSTEM TYPES - Task 4.1
// =====================================================

// AI Generation Job Interface
export interface AIGenerationJob {
  id: string;
  tool_id: string;
  job_type: 'scrape' | 'generate' | 'bulk' | 'media_extraction';
  status: JobStatus;
  progress: number;
  scraped_data?: any;
  ai_prompts?: any;
  ai_responses?: any;
  error_logs?: any;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

// Media Asset Interface
export interface MediaAsset {
  id: string;
  tool_id: string;
  asset_type: 'logo' | 'favicon' | 'screenshot' | 'og_image';
  source_url?: string;
  local_path?: string;
  cdn_url?: string;
  file_size?: number;
  mime_type?: string;
  width?: number;
  height?: number;
  alt_text?: string;
  is_primary: boolean;
  extraction_method?: string;
  created_at: string;
  updated_at: string;
}

// Editorial Review Interface (Database Schema)
export interface EditorialReview {
  id: string;
  tool_id: string;
  reviewed_by: string;
  review_status: 'pending' | 'approved' | 'rejected' | 'needs_revision';
  review_date: string;
  featured_date?: string;
  review_notes?: string;
  editorial_text?: string;
  quality_score?: number;
  content_flags?: any;
  approval_workflow?: any;
  created_at: string;
  updated_at: string;
}

// Internal Editorial Review Interface (for application logic)
export interface InternalEditorialReview {
  id: string;
  toolId: string;
  reviewedBy: string;
  reviewStatus: 'pending' | 'approved' | 'rejected' | 'needs_revision';
  reviewDate: string;
  featuredDate?: string;
  reviewNotes?: string;
  editorialText?: string;
  qualityScore?: number;
  contentFlags?: any;
  approvalWorkflow?: any;
  createdAt: string;
  updatedAt: string;
}

// Bulk Processing Job Interface (Database Schema)
export interface BulkProcessingJob {
  id: string;
  job_type: 'text_file' | 'json_file' | 'manual_entry' | 'csv_import';
  status: JobStatus;
  total_items: number;
  processed_items: number;
  successful_items: number;
  failed_items: number;
  source_data: any;
  processing_options?: any;
  progress_log?: any;
  created_by: string;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
}

// Internal Bulk Processing Job Interface (for application logic)
export interface InternalBulkProcessingJob {
  id: string;
  jobType: 'text_file' | 'json_file' | 'manual_entry' | 'csv_import';
  status: JobStatus;
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  sourceData: any;
  processingOptions?: any;
  results?: {
    successful: Array<{
      toolId: string;
      url: string;
      generatedAt: string;
    }>;
    failed: Array<{
      url: string;
      error: string;
      timestamp: string;
    }>;
  };
  progressLog?: any;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
}

// System Configuration Interface
export interface SystemConfiguration {
  id: string;
  config_key: string;
  config_value: any;
  config_type: 'ai_provider' | 'scraping' | 'job_processing' | 'system' | 'security';
  is_sensitive: boolean;
  is_active: boolean;
  description?: string;
  validation_schema?: any;
  updated_by?: string;
  version: number;
  created_at: string;
  updated_at: string;
}
