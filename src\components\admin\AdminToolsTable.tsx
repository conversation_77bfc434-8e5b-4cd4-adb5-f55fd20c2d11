'use client';

import { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { DbTool, ContentStatus } from '@/lib/types';

interface AdminToolsTableProps {
  tools: DbTool[];
  selectedToolIds: string[];
  onToolSelection: (toolId: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  onUpdateStatus: (toolId: string, status: ContentStatus) => Promise<void>;
  onDeleteTool: (toolId: string) => Promise<void>;
}

interface SortConfig {
  key: keyof DbTool;
  direction: 'asc' | 'desc';
}

/**
 * Tool Status Badge Component
 */
function ToolStatusBadge({ status }: { status: string }): React.JSX.Element {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'published':
        return { color: 'bg-green-500/20 text-green-400 border-green-500/30', icon: '🌐' };
      case 'draft':
        return { color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30', icon: '📝' };
      case 'under_review':
        return { color: 'bg-blue-500/20 text-blue-400 border-blue-500/30', icon: '👀' };
      case 'approved':
        return { color: 'bg-green-600/20 text-green-300 border-green-600/30', icon: '✅' };
      case 'rejected':
        return { color: 'bg-red-500/20 text-red-400 border-red-500/30', icon: '❌' };
      case 'archived':
        return { color: 'bg-gray-500/20 text-gray-400 border-gray-500/30', icon: '📦' };
      default:
        return { color: 'bg-gray-500/20 text-gray-400 border-gray-500/30', icon: '❓' };
    }
  };

  const config = getStatusConfig(status);

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${config.color}`}>
      <span className="mr-1">{config.icon}</span>
      {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
    </span>
  );
}

/**
 * Individual Tool Actions Component
 */
function ToolActions({
  tool,
  onUpdateStatus,
  onDeleteTool,
  onEdit,
}: {
  tool: DbTool;
  onUpdateStatus: (toolId: string, status: ContentStatus) => Promise<void>;
  onDeleteTool: (toolId: string) => Promise<void>;
  onEdit: (toolId: string) => void;
}): React.JSX.Element {
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusUpdate = async (status: ContentStatus) => {
    setIsUpdating(true);
    try {
      await onUpdateStatus(tool.id, status);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this tool?')) return;
    await onDeleteTool(tool.id);
  };

  return (
    <div className="flex items-center space-x-2 text-sm">
      {/* Status-specific actions */}
      {(tool.content_status === 'draft' || !tool.content_status) && (
        <button
          onClick={() => handleStatusUpdate('published')}
          disabled={isUpdating}
          className="text-green-400 hover:text-green-300 disabled:opacity-50"
        >
          Publish
        </button>
      )}
      {tool.content_status === 'published' && (
        <button
          onClick={() => handleStatusUpdate('draft')}
          disabled={isUpdating}
          className="text-yellow-400 hover:text-yellow-300 disabled:opacity-50"
        >
          Unpublish
        </button>
      )}
      {tool.content_status === 'archived' && (
        <button
          onClick={() => handleStatusUpdate('draft')}
          disabled={isUpdating}
          className="text-blue-400 hover:text-blue-300 disabled:opacity-50"
        >
          Restore
        </button>
      )}
      
      {/* Common actions */}
      <button
        onClick={() => onEdit(tool.id)}
        className="text-blue-400 hover:text-blue-300"
      >
        Edit
      </button>
      <button
        onClick={handleDelete}
        className="text-red-400 hover:text-red-300"
      >
        Delete
      </button>
    </div>
  );
}

/**
 * Admin Tools Table Component
 * 
 * Enhanced table component with multi-select functionality for bulk operations.
 * Features:
 * - Sortable columns
 * - Row selection with checkboxes
 * - Status badges with consistent styling
 * - Individual tool actions
 * - Select all functionality
 */
export function AdminToolsTable({
  tools,
  selectedToolIds,
  onToolSelection,
  onSelectAll,
  onUpdateStatus,
  onDeleteTool,
}: AdminToolsTableProps): React.JSX.Element {
  const router = useRouter();
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: 'created_at', direction: 'desc' });

  // Sort tools based on current sort configuration
  const sortedTools = useMemo(() => {
    const sorted = [...tools].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortConfig.direction === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });

    return sorted;
  }, [tools, sortConfig]);

  const handleSort = (key: keyof DbTool) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectAll = (checked: boolean) => {
    onSelectAll(checked);
  };

  const isAllSelected = tools.length > 0 && selectedToolIds.length === tools.length;
  const isPartiallySelected = selectedToolIds.length > 0 && selectedToolIds.length < tools.length;

  return (
    <div className="bg-zinc-800 rounded-lg border border-zinc-700 overflow-hidden">
      <div className="p-6 border-b border-zinc-700">
        <h2 className="text-xl font-semibold text-white">Manage Tools</h2>
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-zinc-700">
            <tr>
              <th className="px-6 py-3 text-left w-12">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={(input) => {
                    if (input) input.indeterminate = isPartiallySelected;
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500"
                />
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white"
                onClick={() => handleSort('name')}
              >
                Tool {sortConfig.key === 'name' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white"
                onClick={() => handleSort('category_id')}
              >
                Category {sortConfig.key === 'category_id' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white"
                onClick={() => handleSort('content_status')}
              >
                Status {sortConfig.key === 'content_status' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider cursor-pointer hover:text-white"
                onClick={() => handleSort('created_at')}
              >
                Created {sortConfig.key === 'created_at' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-zinc-700">
            {sortedTools.map((tool) => (
              <tr 
                key={tool.id} 
                className={`hover:bg-zinc-700/50 transition-colors ${
                  selectedToolIds.includes(tool.id) ? 'bg-orange-500/10' : ''
                }`}
              >
                <td className="px-6 py-4" onClick={(e) => e.stopPropagation()}>
                  <input
                    type="checkbox"
                    checked={selectedToolIds.includes(tool.id)}
                    onChange={(e) => onToolSelection(tool.id, e.target.checked)}
                    className="rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <img
                      src={tool.logo_url || '/placeholder-logo.png'}
                      alt={tool.name || 'Tool logo'}
                      className="w-8 h-8 rounded mr-3"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder-logo.png';
                      }}
                    />
                    <div>
                      <div className="text-sm font-medium text-white">{tool.name || 'Unnamed Tool'}</div>
                      <div className="text-sm text-gray-400 truncate max-w-xs">
                        {tool.description || 'No description available'}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="text-sm text-gray-300">{tool.category_id || 'Uncategorized'}</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <ToolStatusBadge status={tool.content_status || 'draft'} />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                  {tool.created_at ? new Date(tool.created_at).toLocaleDateString() : 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <ToolActions
                    tool={tool}
                    onUpdateStatus={onUpdateStatus}
                    onDeleteTool={onDeleteTool}
                    onEdit={(toolId) => router.push(`/admin/tools/${toolId}/edit`)}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
